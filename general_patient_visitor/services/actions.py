from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import get_patient_profile
from base.constants import ReviewStatus
from base.services.incidents.base import IncidentService
from base.services.responses import APIResponse
from general_patient_visitor.models import GeneralPatientVisitor
from base.services.logging.logger import LoggingService
from general_patient_visitor.serializers import (
    GeneralPatientVisitorUpdateVersionSerializer,
    GeneralPatientVisitorVersionSerializer,
    IncidentListSerializer,
)
from incidents.services.workflow import IncidentWorkflow
from rest_framework import status
from django.core.exceptions import ValidationError
from activities.services import ActivityService
from activities.models import ActivityType

from incidents.views.send_to_department import send_incident_submission_email


logging_service = LoggingService()
user_profile_service = UserProfileService()


class GPVActionsService:
    """
    This class contains methods to handle actions related to General Patient Visitors (GPVs).
    """

    def __init__(self, user, incident_id, data):
        self.user = user
        self.data = data
        self.incident_id = incident_id
        self.workflow_services = IncidentWorkflow(
            model=GeneralPatientVisitor, user=self.user
        )
        self.general_incident = IncidentService()
        try:
            self.incident = (
                GeneralPatientVisitor.objects.select_related(
                    "patient_visitor",
                    "physician_notified",
                    "family_notified",
                    "report_facility",
                    "department",
                    "created_by",
                )
                .prefetch_related(
                    "documents",
                    "reviews",
                )
                .get(id=incident_id)
            )
        except GeneralPatientVisitor.DoesNotExist:
            self.incident = None

    def modify_incident(self) -> APIResponse:
        """
        Modifies an existing GPV incident with the provided self.data.
        """
        try:
            report_facility = self.incident.report_facility

            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, self.incident.report_facility)
                and not is_manager_user(self.user, self.incident.department)
            ) and not self.incident.created_by == self.user:
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    code=status.HTTP_403_FORBIDDEN,
                    data=None,
                )

            # Update patient visitor
            if "patient_visitor" in self.data:
                patient_profile = user_profile_service.get_or_create_profile(
                    self.data["patient_visitor"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["patient_visitor"] = patient_profile.data.id

            # Update physician notified
            if "physician_notified" in self.data:
                physician_profile = user_profile_service.get_or_create_profile(
                    self.data["physician_notified"]
                )
                if not physician_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["physician_notified"] = physician_profile.data.id

            # Update family notified
            if "family_notified" in self.data:
                family_profile = user_profile_service.get_or_create_profile(
                    self.data["family_notified"]
                )
                if not family_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["family_notified"] = family_profile.data.id
            
            if "notified_by" in self.data:
                notified_by_profile = user_profile_service.get_or_create_profile(
                    self.data["notified_by"]
                )
                if not notified_by_profile.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_profile.message,
                        code=status.HTTP_400_BAD_REQUEST,
                        data=None,
                    )
                self.data["notified_by"] = notified_by_profile.data.id

            self.data["original_report"] = self.incident.id
            self.data["report_facility"] = report_facility.id
            self.data["created_by"] = self.user.id
            self.data["status"] = self.data.get("status", ReviewStatus.DRAFT)

            self.data["department"] = self.incident.department.id
            self.data["created_by"] = self.user.id

            version_serializer = GeneralPatientVisitorUpdateVersionSerializer(
                data=self.data
            )

            if version_serializer.is_valid():
                version_serializer.save()
                old_status = self.incident.status
                self.incident.is_modified = True
                self.incident.updated_by = self.user
                self.incident.status = self.data.get("status", ReviewStatus.DRAFT)
                self.incident.save()

                # Log modification activity
                ActivityService.log_activity(
                    user=self.user,
                    content_object=self.incident,
                    activity_type=ActivityType.UPDATED,
                    description="Incident modified",
                    details={
                        'incident_type': 'General Patient/Visitor',
                        'modification_type': 'workflow_modification',
                        'is_modified': True
                    }
                )

                # Log status change if applicable
                new_status = self.incident.status
                if old_status != new_status:
                    ActivityService.log_status_change(
                        user=self.user,
                        content_object=self.incident,
                        old_status=old_status,
                        new_status=new_status,
                        details={'incident_type': 'General Patient/Visitor'}
                    )

                if self.incident.status == ReviewStatus.OPEN:
                    send_incident_submission_email(
                        incident=self.incident,
                        incident_type=self.incident.incident_type,
                    )

                    # Log submission activity
                    ActivityService.log_activity(
                        user=self.user,
                        content_object=self.incident,
                        activity_type=ActivityType.NOTIFICATION_SENT,
                        description="Incident submission notification sent",
                        details={
                            'incident_type': 'General Patient/Visitor',
                            'notification_type': 'submission_email'
                        }
                    )

                return APIResponse(
                    success=True,
                    message="Incident modified successfully",
                    code=status.HTTP_200_OK,
                    data=version_serializer.data,
                )
            else:
                logging_service.log_error(version_serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    code=status.HTTP_400_BAD_REQUEST,
                    data=version_serializer.errors,
                )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                code=status.HTTP_404_NOT_FOUND,
                data=None,
            )

        except ValidationError as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Validation error",
                code=status.HTTP_400_BAD_REQUEST,
                data=None,
            )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while modifying the incident",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def send_for_review(self) -> APIResponse:
        """
        Sends the incident for review.
        """
        try:
            response = self.workflow_services.send_for_a_review(
                self.incident_id,
                self.data,
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            ActivityService.log_review_activity(
                user=self.user,
                content_object=response.data,
                review_type="workflow_review",
                details={
                    'incident_type': 'General Patient/Visitor',
                    'review_action': 'sent_for_review'
                },
                # target_user=
            )

            serializer = IncidentListSerializer(response.data)
            return APIResponse(
                success=True,
                message="Incident sent for review successfully",
                code=status.HTTP_200_OK,
                data=serializer.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while sending the incident for review",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def mark_closed(self) -> APIResponse:
        """
        Marks the incident as resolved/closed.
        """
        try:
            response = self.workflow_services.mark_as_resolved(
                incident=self.incident,
                user=self.user,
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=400,
                    data=None,
                )

            return APIResponse(
                success=True,
                message="Incident marked as closed successfully",
                code=status.HTTP_200_OK,
                data=None,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while marking the incident as closed",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )

    def delete_gpv_draft_incidents(self) -> APIResponse:
        """
        Deletes draft incidents for General Patient Visitors.
        """
        try:
            response = self.workflow_services.delete_drafts(
                user=self.user,
                model=GeneralPatientVisitor,
                incident_ids=self.data.get("incident_ids", None),
            )

            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    code=response.code,
                    data=None,
                )

            return APIResponse(
                success=True,
                message="Draft incidents deleted successfully",
                code=status.HTTP_200_OK,
                data=response.data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while deleting draft incidents",
                code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data=None,
            )
