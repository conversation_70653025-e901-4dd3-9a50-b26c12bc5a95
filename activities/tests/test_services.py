from django.test import TestCase
from django.contrib.auth.models import User
from activities.services import ActivityService
from activities.models import ActivityLog, ActivityType
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Facility, Department


class ActivityServiceTest(TestCase):
    """Test cases for the ActivityService"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.target_user = User.objects.create_user(
            username='targetuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_type='General Patient/Visitor',
            status='Draft',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )
    
    def test_log_activity_basic(self):
        """Test basic activity logging"""
        activity = ActivityService.log_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.CREATED,
            description='Test incident created'
        )
        
        self.assertIsInstance(activity, ActivityLog)
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.CREATED)
        self.assertEqual(activity.content_object, self.incident)
        self.assertEqual(activity.description, 'Test incident created')
    
    def test_log_activity_with_details(self):
        """Test activity logging with details"""
        details = {
            'incident_type': 'General Patient/Visitor',
            'changed_fields': ['status', 'description']
        }
        
        activity = ActivityService.log_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.UPDATED,
            details=details,
            facility=self.facility,
            department=self.department
        )
        
        self.assertEqual(activity.details, details)
        self.assertEqual(activity.facility, self.facility)
        self.assertEqual(activity.department, self.department)
    
    def test_log_creation(self):
        """Test the log_creation convenience method"""
        activity = ActivityService.log_creation(
            user=self.user,
            content_object=self.incident
        )
        
        self.assertEqual(activity.activity_type, ActivityType.CREATED)
        self.assertIn('created', activity.description.lower())
    
    def test_log_update(self):
        """Test the log_update convenience method"""
        changed_fields = ['status', 'description', 'priority']
        
        activity = ActivityService.log_update(
            user=self.user,
            content_object=self.incident,
            changed_fields=changed_fields
        )
        
        self.assertEqual(activity.activity_type, ActivityType.UPDATED)
        self.assertEqual(activity.details['changed_fields'], changed_fields)
        self.assertIn('updated', activity.description.lower())
    
    def test_log_status_change(self):
        """Test the log_status_change convenience method"""
        old_status = 'Draft'
        new_status = 'Open'
        
        activity = ActivityService.log_status_change(
            user=self.user,
            content_object=self.incident,
            old_status=old_status,
            new_status=new_status
        )
        
        self.assertEqual(activity.activity_type, ActivityType.STATUS_CHANGED)
        self.assertEqual(activity.details['old_status'], old_status)
        self.assertEqual(activity.details['new_status'], new_status)
    
    def test_log_department_transfer_outgoing(self):
        """Test logging department transfer (outgoing)"""
        to_department = Department.objects.create(
            name='Quality Assurance',
            facility=self.facility
        )
        
        activity = ActivityService.log_department_transfer(
            user=self.user,
            content_object=self.incident,
            from_department=self.department,
            to_department=to_department,
            is_outgoing=True
        )
        
        self.assertEqual(activity.activity_type, ActivityType.SENT_TO_DEPARTMENT)
        self.assertEqual(activity.department, to_department)
        self.assertEqual(activity.details['department_name'], 'Quality Assurance')
        self.assertEqual(activity.details['to_department_id'], to_department.id)
    
    def test_log_department_transfer_incoming(self):
        """Test logging department transfer (incoming)"""
        from_department = Department.objects.create(
            name='Quality Assurance',
            facility=self.facility
        )
        
        activity = ActivityService.log_department_transfer(
            user=self.user,
            content_object=self.incident,
            from_department=from_department,
            to_department=self.department,
            is_outgoing=False
        )
        
        self.assertEqual(activity.activity_type, ActivityType.SENT_FROM_DEPARTMENT)
        self.assertEqual(activity.department, from_department)
        self.assertEqual(activity.details['department_name'], 'Quality Assurance')
    
    def test_log_document_activity_addition(self):
        """Test logging document addition"""
        document_names = ['report.pdf', 'evidence.jpg']
        
        activity = ActivityService.log_document_activity(
            user=self.user,
            content_object=self.incident,
            document_count=2,
            document_names=document_names,
            is_addition=True
        )
        
        self.assertEqual(activity.activity_type, ActivityType.DOCUMENT_ADDED)
        self.assertEqual(activity.details['document_count'], 2)
        self.assertEqual(activity.details['document_names'], document_names)
    
    def test_log_document_activity_removal(self):
        """Test logging document removal"""
        activity = ActivityService.log_document_activity(
            user=self.user,
            content_object=self.incident,
            document_count=1,
            is_addition=False
        )
        
        self.assertEqual(activity.activity_type, ActivityType.DOCUMENT_REMOVED)
        self.assertEqual(activity.details['document_count'], 1)
    
    def test_log_assignment(self):
        """Test logging user assignment"""
        activity = ActivityService.log_assignment(
            user=self.user,
            content_object=self.incident,
            assignee=self.target_user,
            is_assignment=True
        )
        
        self.assertEqual(activity.activity_type, ActivityType.ASSIGNED)
        self.assertEqual(activity.target_user, self.target_user)
        self.assertEqual(activity.details['assignee_id'], self.target_user.id)
        self.assertIn('assignee_name', activity.details)
    
    def test_log_unassignment(self):
        """Test logging user unassignment"""
        activity = ActivityService.log_assignment(
            user=self.user,
            content_object=self.incident,
            assignee=self.target_user,
            is_assignment=False
        )
        
        self.assertEqual(activity.activity_type, ActivityType.UNASSIGNED)
        self.assertEqual(activity.target_user, self.target_user)
    
    def test_log_review_activity(self):
        """Test logging review activity"""
        activity = ActivityService.log_review_activity(
            user=self.user,
            content_object=self.incident,
            review_type='quality_review'
        )
        
        self.assertEqual(activity.activity_type, ActivityType.REVIEW_ADDED)
        self.assertEqual(activity.details['review_type'], 'quality_review')
    
    def test_log_resolution(self):
        """Test logging incident resolution"""
        activity = ActivityService.log_resolution(
            user=self.user,
            content_object=self.incident,
            is_resolved=True
        )
        
        self.assertEqual(activity.activity_type, ActivityType.RESOLVED)
    
    def test_log_reopening(self):
        """Test logging incident reopening"""
        activity = ActivityService.log_resolution(
            user=self.user,
            content_object=self.incident,
            is_resolved=False
        )
        
        self.assertEqual(activity.activity_type, ActivityType.REOPENED)
    
    def test_log_investigation_started(self):
        """Test logging investigation start"""
        activity = ActivityService.log_investigation_activity(
            user=self.user,
            content_object=self.incident,
            is_started=True
        )
        
        self.assertEqual(activity.activity_type, ActivityType.INVESTIGATION_STARTED)
    
    def test_log_investigation_completed(self):
        """Test logging investigation completion"""
        activity = ActivityService.log_investigation_activity(
            user=self.user,
            content_object=self.incident,
            is_started=False
        )
        
        self.assertEqual(activity.activity_type, ActivityType.INVESTIGATION_COMPLETED)
    
    def test_log_workflow_step(self):
        """Test logging workflow step completion"""
        step_name = 'Quality Review'
        
        activity = ActivityService.log_workflow_step(
            user=self.user,
            content_object=self.incident,
            step_name=step_name
        )
        
        self.assertEqual(activity.activity_type, ActivityType.WORKFLOW_STEP_COMPLETED)
        self.assertEqual(activity.details['step_name'], step_name)
    
    def test_get_activities_for_object(self):
        """Test getting all activities for an object"""
        # Create multiple activities
        ActivityService.log_creation(user=self.user, content_object=self.incident)
        ActivityService.log_update(user=self.user, content_object=self.incident)
        ActivityService.log_status_change(
            user=self.user, 
            content_object=self.incident,
            old_status='Draft',
            new_status='Open'
        )
        
        activities = ActivityService.get_activities_for_object(self.incident)
        
        self.assertEqual(activities.count(), 3)
        # Should be ordered by timestamp descending
        self.assertEqual(activities[0].activity_type, ActivityType.STATUS_CHANGED)
        self.assertEqual(activities[1].activity_type, ActivityType.UPDATED)
        self.assertEqual(activities[2].activity_type, ActivityType.CREATED)
    
    def test_auto_detect_facility_and_department(self):
        """Test auto-detection of facility and department from content object"""
        activity = ActivityService.log_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.CREATED
        )
        
        # Should auto-detect facility and department from the incident
        self.assertEqual(activity.facility, self.facility)
        self.assertEqual(activity.department, self.department)
    
    def test_activity_without_user(self):
        """Test logging system activities without a user"""
        activity = ActivityService.log_activity(
            user=None,
            content_object=self.incident,
            activity_type=ActivityType.NOTIFICATION_SENT,
            description='System notification sent'
        )
        
        self.assertIsNone(activity.user)
        self.assertEqual(activity.activity_type, ActivityType.NOTIFICATION_SENT)

    def test_activity_with_ip_and_user_agent(self):
        """Test logging activity with IP address and user agent"""
        ip_address = '***********'
        user_agent = 'Mozilla/5.0 (Test Browser)'

        activity = ActivityService.log_activity(
            user=self.user,
            content_object=self.incident,
            activity_type=ActivityType.CREATED,
            ip_address=ip_address,
            user_agent=user_agent
        )

        self.assertEqual(activity.ip_address, ip_address)
        self.assertEqual(activity.user_agent, user_agent)
